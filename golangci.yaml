version: "2"
run:
  timeout: 6m
linters:
  default: none
  enable:
    - bodyclose
    - copyloopvar
    - dogsled
    - errcheck
    - gocritic
    - gosec
    - govet
    - ineffassign
    - intrange
    - misspell
    - nakedret
    - revive
    - staticcheck
    - unconvert
    - unused
    - whitespace
  settings:
    govet:
      enable:
        - fieldalignment
  exclusions:
    generated: lax
    rules:
      - linters:
          - govet
        text: pointer bytes could be
    paths:
      - third_party$
      - builtin$
      - examples$
issues:
  new-from-rev: 91593cf91797ca0a98ffa31842107a9d916da37b
formatters:
  enable:
    - goimports
  settings:
    goimports:
      local-prefixes:
        - github.com/buildpacks/lifecycle
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
