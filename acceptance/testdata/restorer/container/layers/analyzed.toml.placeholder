[image]
reference = "some-previous-image-ref"

[[metadata.buildpacks]]
key = "some-buildpack-id"

[metadata.buildpacks.layers.launch-layer]
launch = true
sha = "launch-sha"

  [metadata.buildpacks.layers.launch-layer.data]
  launch-key = "launch-value"

[metadata.buildpacks.layers.launch-build-layer]
build = true
launch = true
sha = "launch-build-sha"

  [metadata.buildpacks.layers.launch-build-layer.data]
  launch-build-key = "launch-build-value"

[metadata.buildpacks.layers.launch-build-cache-layer]
build = true
cache = true
launch = true
sha = "launch-build-cache-sha"

  [metadata.buildpacks.layers.launch-build-cache-layer.data]
  launch-build-cache-key = "launch-build-cache-value"

[metadata.buildpacks.layers.launch-cache-layer]
cache = true
launch = true
sha = "launch-cache-sha"

  [metadata.buildpacks.layers.launch-cache-layer.data]
  launch-cache-key = "launch-cache-value"

[metadata.buildpacks.layers.launch-false-layer]
sha = "launch-false-sha"

  [metadata.buildpacks.layers.launch-false-layer.data]
  launch-false-key = "launch-false-value"

[metadata.buildpacks.store.metadata.metadata-buildpack-store-data]
store-key = "store-val"
