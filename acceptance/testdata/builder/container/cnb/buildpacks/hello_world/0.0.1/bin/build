#!/usr/bin/env bash
set -eo pipefail

echo "---> Hello World buildpack"

# INPUT ARGUMENTS
platform_dir=$2
env_dir=${platform_dir}/env
layers_dir=$1
plan_path=$3

# CNB_APP_DIR
echo "CNB_APP_DIR: ${PWD}"

# PLATFORM DIR
echo "PLATFORM_DIR: ${platform_dir}"

# LAYERS
echo "LAYERS_DIR: ${layers_dir}"

# PLAN
echo "PLAN_PATH: ${plan_path}"
echo "plan contents:"
cat ${plan_path}
echo

# Set default start command
cat > "${layers_dir}/launch.toml" << EOL
[[processes]]
type = "hello"
command = ["echo world"]
args = ["arg1"]
EOL

echo "---> Done"
