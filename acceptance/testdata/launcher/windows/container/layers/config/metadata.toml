[[buildpacks]]
  id = "0.9/buildpack"
  version = "0.0.1"
  api = "0.9"

[[buildpacks]]
  id = "some/buildpack"
  version = "0.0.1"
  api = "0.8"

[[processes]]
  type = "web"
  command = "echo"
  direct = false
  args = ["Executing", "web", "process-type"]
  buildpack-id = "some/buildpack"

[[processes]]
  type = "direct-process"
  command = "ping"
  args = ["/?"]
  direct = true
  buildpack-id = "some/buildpack"

[[processes]]
  type = "indirect-process-with-args"
  command = "test tokens.bat"
  args = ["%VAR1%", "%VAR2%"]
  direct = false
  buildpack-id = "some/buildpack"

[[processes]]
  type = "profile-checker"
  command = "echo"
  args = ["!VAR_FROM_PROFILE!"]
  direct = false
  buildpack-id = "some/buildpack"

[[processes]]
  type = "worker"
  command = "echo"
  args = ["%WORKER_VAR%"]
  direct = false
  buildpack-id = "some/buildpack"

[[processes]]
  type = "exec.d-checker"
  command = "cmd"
  args = ["/c", "echo VAR_FROM_EXEC_D: %VAR_FROM_EXEC_D%"]
  direct = false
  buildpack-id = "0.9/buildpack"

[[processes]]
  type = "process.with.period"
  command = "echo"
  direct = false
  args = ["Executing", "process.with.period", "process-type"]
  buildpack-id = "some/buildpack"
