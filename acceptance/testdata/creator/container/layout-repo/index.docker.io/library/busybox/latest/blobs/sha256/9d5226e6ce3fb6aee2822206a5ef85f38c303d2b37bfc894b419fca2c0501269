{"architecture": "amd64", "config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"], "Cmd": ["sh"], "Image": "sha256:688db7a53b2e8d0358c0e1f309856290bb25ce7acabbf9938f580582e921833f", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "container": "65aaed1d1f89cd3cd5aac9137c4786831e99a845ad823496c6008a22a725c780", "container_config": {"Hostname": "65aaed1d1f89", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"], "Cmd": ["/bin/sh", "-c", "#(nop) ", "CMD [\"sh\"]"], "Image": "sha256:688db7a53b2e8d0358c0e1f309856290bb25ce7acabbf9938f580582e921833f", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": {}}, "created": "2022-11-18T01:19:29.442257773Z", "docker_version": "20.10.12", "history": [{"created": "2022-11-18T01:19:29.321465538Z", "created_by": "/bin/sh -c #(nop) ADD file:36d9f497f679d56737ac1379d93f7b6a2e4c814e38e868a5a8e719c4b226ef6e in / "}, {"created": "2022-11-18T01:19:29.442257773Z", "created_by": "/bin/sh -c #(nop)  CMD [\"sh\"]", "empty_layer": true}], "os": "linux", "rootfs": {"type": "layers", "diff_ids": ["sha256:40cf597a9181e86497f4121c604f9f0ab208950a98ca21db883f26b0a548a2eb"]}}