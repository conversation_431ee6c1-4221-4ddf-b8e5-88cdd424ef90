[image]
  reference = "REPLACE"

[metadata]

  [[metadata.buildpacks]]
    key = "some-buildpack-id"
    version = ""
    [metadata.buildpacks.layers]
      [metadata.buildpacks.layers.launch-build-cache-layer]
        sha = "launch-build-cache-sha"
        build = true
        launch = true
        cache = true
      [metadata.buildpacks.layers.launch-build-layer]
        sha = "launch-build-sha"
        build = true
        launch = true
        cache = false
      [metadata.buildpacks.layers.launch-cache-layer]
        sha = "launch-cache-sha"
        build = false
        launch = true
        cache = true
      [metadata.buildpacks.layers.launch-false-layer]
        sha = "launch-false-sha"
        build = false
        launch = false
        cache = false
      [metadata.buildpacks.layers.launch-layer]
        sha = "launch-sha"
        build = false
        launch = true
        cache = false
    [metadata.buildpacks.store]
      [metadata.buildpacks.store.metadata]
        [metadata.buildpacks.store.metadata.metadata-buildpack-store-data]
          store-key = "store-val"

[run-image]
  reference = "REPLACE"
