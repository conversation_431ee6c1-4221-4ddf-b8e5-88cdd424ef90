{"architecture": "amd64", "created": "0001-01-01T00:00:00Z", "history": [{"author": "some-base-image-author", "created": "2023-03-06T17:34:39.0316521Z", "created_by": "FROM some-base-image"}, {"author": "kaniko", "created": "0001-01-01T00:00:00Z", "created_by": "Layer: 'RUN mkdir /some-dir && echo some-data > /some-dir/some-file && echo some-data > /some-file', Created by extension: first-extension"}, {"author": "kaniko", "created": "0001-01-01T00:00:00Z", "created_by": "Layer: 'RUN mkdir /some-other-dir && echo some-data > /some-other-dir/some-file && echo some-data > /some-other-file', Created by extension: second-extension"}], "os": "linux", "rootfs": {"type": "layers", "diff_ids": ["sha256:b5bb9d8014a0f9b1d61e21e796d78dccdf1352f23cd32812f4850b878ae4944c", "sha256:d8dea3a780ba766c08bd11800809652ce5e9eba50b7b94ac09cb7f5e98e07f08", "sha256:36f3735021a89a605c3da10b9659f0ec69e7c4c72abc802dc32471f1b080fd78"]}, "config": {"Cmd": ["/bin/bash"], "Env": ["PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "CNB_USER_ID=1234", "CNB_GROUP_ID=1000", "CNB_STACK_ID=some-stack-id"], "Labels": {"io.buildpacks.rebasable": "false", "org.opencontainers.image.ref.name": "ubuntu", "org.opencontainers.image.version": "18.04"}, "User": "root"}}