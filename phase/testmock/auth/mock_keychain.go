// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/google/go-containerregistry/pkg/authn (interfaces: Keychain)

// Package testmockauth is a generated GoMock package.
package testmockauth

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	authn "github.com/google/go-containerregistry/pkg/authn"
)

// MockKeychain is a mock of Keychain interface.
type MockKeychain struct {
	ctrl     *gomock.Controller
	recorder *MockKeychainMockRecorder
}

// MockKeychainMockRecorder is the mock recorder for MockKeychain.
type MockKeychainMockRecorder struct {
	mock *MockKeychain
}

// NewMockKeychain creates a new mock instance.
func NewMockKeychain(ctrl *gomock.Controller) *MockKeychain {
	mock := &MockKeychain{ctrl: ctrl}
	mock.recorder = &MockKeychainMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKeychain) EXPECT() *MockKeychainMockRecorder {
	return m.recorder
}

// Resolve mocks base method.
func (m *MockKeychain) Resolve(arg0 authn.Resource) (authn.Authenticator, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Resolve", arg0)
	ret0, _ := ret[0].(authn.Authenticator)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Resolve indicates an expected call of Resolve.
func (mr *MockKeychainMockRecorder) Resolve(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Resolve", reflect.TypeOf((*MockKeychain)(nil).Resolve), arg0)
}
