// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/lifecycle/buildpack (interfaces: BuildExecutor)

// Package testmock is a generated GoMock package.
package testmock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	buildpack "github.com/buildpacks/lifecycle/buildpack"
	log "github.com/buildpacks/lifecycle/log"
)

// MockBuildExecutor is a mock of BuildExecutor interface.
type MockBuildExecutor struct {
	ctrl     *gomock.Controller
	recorder *MockBuildExecutorMockRecorder
}

// MockBuildExecutorMockRecorder is the mock recorder for MockBuildExecutor.
type MockBuildExecutorMockRecorder struct {
	mock *MockBuildExecutor
}

// NewMockBuildExecutor creates a new mock instance.
func NewMockBuildExecutor(ctrl *gomock.Controller) *MockBuildExecutor {
	mock := &MockBuildExecutor{ctrl: ctrl}
	mock.recorder = &MockBuildExecutorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBuildExecutor) EXPECT() *MockBuildExecutorMockRecorder {
	return m.recorder
}

// Build mocks base method.
func (m *MockBuildExecutor) Build(arg0 buildpack.BpDescriptor, arg1 buildpack.BuildInputs, arg2 log.Logger) (buildpack.BuildOutputs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Build", arg0, arg1, arg2)
	ret0, _ := ret[0].(buildpack.BuildOutputs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Build indicates an expected call of Build.
func (mr *MockBuildExecutorMockRecorder) Build(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Build", reflect.TypeOf((*MockBuildExecutor)(nil).Build), arg0, arg1, arg2)
}
