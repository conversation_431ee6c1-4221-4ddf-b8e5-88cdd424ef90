// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/lifecycle/internal/layer (interfaces: SBOMRestorer)

// Package testmock is a generated GoMock package.
package testmock

import (
	reflect "reflect"

	imgutil "github.com/buildpacks/imgutil"
	gomock "github.com/golang/mock/gomock"

	buildpack "github.com/buildpacks/lifecycle/buildpack"
	layer "github.com/buildpacks/lifecycle/internal/layer"
)

// MockSBOMRestorer is a mock of SBOMRestorer interface.
type MockSBOMRestorer struct {
	ctrl     *gomock.Controller
	recorder *MockSBOMRestorerMockRecorder
}

// MockSBOMRestorerMockRecorder is the mock recorder for MockSBOMRestorer.
type MockSBOMRestorerMockRecorder struct {
	mock *MockSBOMRestorer
}

// NewMockSBOMRestorer creates a new mock instance.
func NewMockSBOMRestorer(ctrl *gomock.Controller) *MockSBOMRestorer {
	mock := &MockSBOMRestorer{ctrl: ctrl}
	mock.recorder = &MockSBOMRestorerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSBOMRestorer) EXPECT() *MockSBOMRestorerMockRecorder {
	return m.recorder
}

// RestoreFromCache mocks base method.
func (m *MockSBOMRestorer) RestoreFromCache(arg0 layer.Cache, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestoreFromCache", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestoreFromCache indicates an expected call of RestoreFromCache.
func (mr *MockSBOMRestorerMockRecorder) RestoreFromCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestoreFromCache", reflect.TypeOf((*MockSBOMRestorer)(nil).RestoreFromCache), arg0, arg1)
}

// RestoreFromPrevious mocks base method.
func (m *MockSBOMRestorer) RestoreFromPrevious(arg0 imgutil.Image, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestoreFromPrevious", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestoreFromPrevious indicates an expected call of RestoreFromPrevious.
func (mr *MockSBOMRestorerMockRecorder) RestoreFromPrevious(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestoreFromPrevious", reflect.TypeOf((*MockSBOMRestorer)(nil).RestoreFromPrevious), arg0, arg1)
}

// RestoreToBuildpackLayers mocks base method.
func (m *MockSBOMRestorer) RestoreToBuildpackLayers(arg0 []buildpack.GroupElement) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestoreToBuildpackLayers", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestoreToBuildpackLayers indicates an expected call of RestoreToBuildpackLayers.
func (mr *MockSBOMRestorerMockRecorder) RestoreToBuildpackLayers(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestoreToBuildpackLayers", reflect.TypeOf((*MockSBOMRestorer)(nil).RestoreToBuildpackLayers), arg0)
}
