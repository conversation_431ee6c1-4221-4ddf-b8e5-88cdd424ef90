// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/lifecycle/cache (interfaces: ImageDeleter)

// Package testmockcache is a generated GoMock package.
package testmockcache

import (
	reflect "reflect"

	imgutil "github.com/buildpacks/imgutil"
	gomock "github.com/golang/mock/gomock"
)

// MockImageDeleter is a mock of ImageDeleter interface.
type MockImageDeleter struct {
	ctrl     *gomock.Controller
	recorder *MockImageDeleterMockRecorder
}

// MockImageDeleterMockRecorder is the mock recorder for MockImageDeleter.
type MockImageDeleterMockRecorder struct {
	mock *MockImageDeleter
}

// NewMockImageDeleter creates a new mock instance.
func NewMockImageDeleter(ctrl *gomock.Controller) *MockImageDeleter {
	mock := &MockImageDeleter{ctrl: ctrl}
	mock.recorder = &MockImageDeleterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockImageDeleter) EXPECT() *MockImageDeleterMockRecorder {
	return m.recorder
}

// DeleteImage mocks base method.
func (m *MockImageDeleter) DeleteImage(arg0 imgutil.Image) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "DeleteImage", arg0)
}

// DeleteImage indicates an expected call of DeleteImage.
func (mr *MockImageDeleterMockRecorder) DeleteImage(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteImage", reflect.TypeOf((*MockImageDeleter)(nil).DeleteImage), arg0)
}

// DeleteOrigImageIfDifferentFromNewImage mocks base method.
func (m *MockImageDeleter) DeleteOrigImageIfDifferentFromNewImage(arg0, arg1 imgutil.Image) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "DeleteOrigImageIfDifferentFromNewImage", arg0, arg1)
}

// DeleteOrigImageIfDifferentFromNewImage indicates an expected call of DeleteOrigImageIfDifferentFromNewImage.
func (mr *MockImageDeleterMockRecorder) DeleteOrigImageIfDifferentFromNewImage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOrigImageIfDifferentFromNewImage", reflect.TypeOf((*MockImageDeleter)(nil).DeleteOrigImageIfDifferentFromNewImage), arg0, arg1)
}
