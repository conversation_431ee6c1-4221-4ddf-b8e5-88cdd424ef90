// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/lifecycle/cache (interfaces: ImageComparer)

// Package testmockcache is a generated GoMock package.
package testmockcache

import (
	reflect "reflect"

	imgutil "github.com/buildpacks/imgutil"
	gomock "github.com/golang/mock/gomock"
)

// MockImageComparer is a mock of ImageComparer interface.
type MockImageComparer struct {
	ctrl     *gomock.Controller
	recorder *MockImageComparerMockRecorder
}

// MockImageComparerMockRecorder is the mock recorder for MockImageComparer.
type MockImageComparerMockRecorder struct {
	mock *MockImageComparer
}

// NewMockImageComparer creates a new mock instance.
func NewMockImageComparer(ctrl *gomock.Controller) *MockImageComparer {
	mock := &MockImageComparer{ctrl: ctrl}
	mock.recorder = &MockImageComparerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockImageComparer) EXPECT() *MockImageComparerMockRecorder {
	return m.recorder
}

// ImagesEq mocks base method.
func (m *MockImageComparer) ImagesEq(arg0, arg1 imgutil.Image) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImagesEq", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImagesEq indicates an expected call of ImagesEq.
func (mr *MockImageComparerMockRecorder) ImagesEq(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImagesEq", reflect.TypeOf((*MockImageComparer)(nil).ImagesEq), arg0, arg1)
}
