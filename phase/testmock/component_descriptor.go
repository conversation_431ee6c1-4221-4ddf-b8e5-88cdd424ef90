// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/lifecycle/buildpack (interfaces: Descriptor)

// Package testmock is a generated GoMock package.
package testmock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	buildpack "github.com/buildpacks/lifecycle/buildpack"
)

// MockDescriptor is a mock of Descriptor interface.
type MockDescriptor struct {
	ctrl     *gomock.Controller
	recorder *MockDescriptorMockRecorder
}

// MockDescriptorMockRecorder is the mock recorder for MockDescriptor.
type MockDescriptorMockRecorder struct {
	mock *MockDescriptor
}

// NewMockDescriptor creates a new mock instance.
func NewMockDescriptor(ctrl *gomock.Controller) *MockDescriptor {
	mock := &MockDescriptor{ctrl: ctrl}
	mock.recorder = &MockDescriptorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDescriptor) EXPECT() *MockDescriptorMockRecorder {
	return m.recorder
}

// API mocks base method.
func (m *MockDescriptor) API() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "API")
	ret0, _ := ret[0].(string)
	return ret0
}

// API indicates an expected call of API.
func (mr *MockDescriptorMockRecorder) API() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "API", reflect.TypeOf((*MockDescriptor)(nil).API))
}

// Homepage mocks base method.
func (m *MockDescriptor) Homepage() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Homepage")
	ret0, _ := ret[0].(string)
	return ret0
}

// Homepage indicates an expected call of Homepage.
func (mr *MockDescriptorMockRecorder) Homepage() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Homepage", reflect.TypeOf((*MockDescriptor)(nil).Homepage))
}

// TargetsList mocks base method.
func (m *MockDescriptor) TargetsList() []buildpack.TargetMetadata {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TargetsList")
	ret0, _ := ret[0].([]buildpack.TargetMetadata)
	return ret0
}

// TargetsList indicates an expected call of TargetsList.
func (mr *MockDescriptorMockRecorder) TargetsList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TargetsList", reflect.TypeOf((*MockDescriptor)(nil).TargetsList))
}
