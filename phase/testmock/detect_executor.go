// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/lifecycle/buildpack (interfaces: DetectExecutor)

// Package testmock is a generated GoMock package.
package testmock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	buildpack "github.com/buildpacks/lifecycle/buildpack"
	log "github.com/buildpacks/lifecycle/log"
)

// MockDetectExecutor is a mock of DetectExecutor interface.
type MockDetectExecutor struct {
	ctrl     *gomock.Controller
	recorder *MockDetectExecutorMockRecorder
}

// MockDetectExecutorMockRecorder is the mock recorder for MockDetectExecutor.
type MockDetectExecutorMockRecorder struct {
	mock *MockDetectExecutor
}

// NewMockDetectExecutor creates a new mock instance.
func NewMockDetectExecutor(ctrl *gomock.Controller) *MockDetectExecutor {
	mock := &MockDetectExecutor{ctrl: ctrl}
	mock.recorder = &MockDetectExecutorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDetectExecutor) EXPECT() *MockDetectExecutorMockRecorder {
	return m.recorder
}

// Detect mocks base method.
func (m *MockDetectExecutor) Detect(arg0 buildpack.Descriptor, arg1 buildpack.DetectInputs, arg2 log.Logger) buildpack.DetectOutputs {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Detect", arg0, arg1, arg2)
	ret0, _ := ret[0].(buildpack.DetectOutputs)
	return ret0
}

// Detect indicates an expected call of Detect.
func (mr *MockDetectExecutorMockRecorder) Detect(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Detect", reflect.TypeOf((*MockDetectExecutor)(nil).Detect), arg0, arg1, arg2)
}
