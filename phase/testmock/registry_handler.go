// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/lifecycle/image (interfaces: RegistryHandler)

// Package testmock is a generated GoMock package.
package testmock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockRegistryHandler is a mock of Registry<PERSON>andler interface.
type MockRegistryHandler struct {
	ctrl     *gomock.Controller
	recorder *MockRegistryHandlerMockRecorder
}

// MockRegistryHandlerMockRecorder is the mock recorder for MockRegistryHandler.
type MockRegistryHandlerMockRecorder struct {
	mock *MockRegistryHandler
}

// NewMockRegistryHandler creates a new mock instance.
func NewMockRegistryHandler(ctrl *gomock.Controller) *MockRegistryHandler {
	mock := &MockRegistryHandler{ctrl: ctrl}
	mock.recorder = &MockRegistryHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRegistryHandler) EXPECT() *MockRegistryHandlerMockRecorder {
	return m.recorder
}

// EnsureReadAccess mocks base method.
func (m *MockRegistryHandler) EnsureReadAccess(arg0 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range arg0 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EnsureReadAccess", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// EnsureReadAccess indicates an expected call of EnsureReadAccess.
func (mr *MockRegistryHandlerMockRecorder) EnsureReadAccess(arg0 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureReadAccess", reflect.TypeOf((*MockRegistryHandler)(nil).EnsureReadAccess), arg0...)
}

// EnsureWriteAccess mocks base method.
func (m *MockRegistryHandler) EnsureWriteAccess(arg0 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range arg0 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EnsureWriteAccess", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// EnsureWriteAccess indicates an expected call of EnsureWriteAccess.
func (mr *MockRegistryHandlerMockRecorder) EnsureWriteAccess(arg0 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureWriteAccess", reflect.TypeOf((*MockRegistryHandler)(nil).EnsureWriteAccess), arg0...)
}
