#!/bin/bash

set -euo pipefail

output_dir=$1
platform_dir=$2
plan_path=$3

bp_dir=$(cd $(dirname "$0")/.. && pwd)
bp_id=$(cat "$bp_dir/extension.toml"|yj -t|jq -r .extension.id)
bp_version=$(cat "$bp_dir/extension.toml"|yj -t|jq -r .extension.version)

echo "build out: ${bp_id}@${bp_version}"
>&2 echo "build err: ${bp_id}@${bp_version}"

echo "TEST_ENV: ${TEST_ENV}" > "build-info-${bp_id}-${bp_version}"
echo -n "${CNB_BP_PLAN_PATH:-unset}" > "build-env-cnb-bp-plan-path-${bp_id}-${bp_version}"
echo -n "${CNB_EXTENSION_DIR:-unset}" > "build-env-cnb-extension-dir-${bp_id}-${bp_version}"
echo -n "${CNB_LAYERS_DIR:-unset}" > "build-env-cnb-layers-dir-${bp_id}-${bp_version}"
echo -n "${CNB_OUTPUT_DIR:-unset}" > "build-env-cnb-output-dir-${bp_id}-${bp_version}"
echo -n "${CNB_PLATFORM_DIR:-unset}" > "build-env-cnb-platform-dir-${bp_id}-${bp_version}"

cp -a "$platform_dir/env" "build-env-${bp_id}-${bp_version}"

cat "$plan_path" > "build-plan-in-${bp_id}-${bp_version}.toml"

if [[ -f build.Dockerfile-${bp_id}-${bp_version} ]]; then
  cat "build.Dockerfile-${bp_id}-${bp_version}" > "$output_dir/build.Dockerfile"
fi

if [[ -f run.Dockerfile-${bp_id}-${bp_version} ]]; then
  cat "run.Dockerfile-${bp_id}-${bp_version}" > "$output_dir/run.Dockerfile"
fi

if [[ -f extend-config-${bp_id}-${bp_version}.toml ]]; then
  cat "extend-config-${bp_id}-${bp_version}.toml" > "$output_dir/extend-config.toml"
fi

if [[ -f build-status-${bp_id}-${bp_version} ]]; then
  exit "$(cat "build-status-${bp_id}-${bp_version}")"
fi

if [[ -f build-status ]]; then
  exit "$(cat build-status)"
fi

exit 0
