package cmd

import (
	"fmt"
	"os"
	"strings"
)

const (
	CodeForFailed = 1
	// 2: reserved
	CodeForInvalidArgs = 3
	// 4: CodeForInvalidEnv
	// 5: CodeForNotFound
	// 9: CodeForFailedUpdate

	CodeForIncompatiblePlatformAPI  = 11
	CodeForIncompatibleBuildpackAPI = 12
)

type ErrorFail struct {
	Err    error
	Code   int
	Action []string
}

func (e *ErrorFail) Error() string {
	message := "failed to " + strings.Join(e.Action, " ")
	if e.Err == nil {
		return message
	}
	return fmt.Sprintf("%s: %s", message, e.Err)
}

func FailCode(code int, action ...string) *ErrorFail {
	return FailErrCode(nil, code, action...)
}

func FailErr(err error, action ...string) *ErrorFail {
	code := CodeForFailed
	if err, ok := err.(*ErrorFail); ok {
		code = err.Code
	}
	return FailErrCode(err, code, action...)
}

func FailErrCode(err error, code int, action ...string) *ErrorFail {
	return &ErrorFail{Err: err, Code: code, Action: action}
}

func Exit(err error) {
	if err == nil {
		os.Exit(0)
	}
	DefaultLogger.Errorf("%s\n", err)
	if err, ok := err.(*ErrorFail); ok {
		os.Exit(err.Code)
	}
	os.Exit(CodeForFailed)
}

func ExitWithVersion() {
	DefaultLogger.Info(buildVersion())
	os.Exit(0)
}
