name: post-release

on:
  release:
    types:
      - published # trigger for releases and pre-releases

jobs:
  retag-lifecycle-images:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - name: Setup go
        uses: actions/setup-go@v5
        with:
          check-latest: true
          go-version-file: 'go.mod'
      - name: Install crane
        run: |
          go install github.com/google/go-containerregistry/cmd/crane@latest
      - name: Install cosign
        uses: sigstore/cosign-installer@v3
      - uses: azure/docker-login@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Set env
        run: |
          echo "LIFECYCLE_VERSION=$(echo ${{ github.event.release.tag_name }} | cut -d "v" -f2)" >> $GITHUB_ENV
          echo "LIFECYCLE_IMAGE_TAG=$(git describe --always --abbrev=7)" >> $GITHUB_ENV
      - name: Verify lifecycle images
        run: |
          LINUX_AMD64_SHA=$(cosign verify --certificate-identity-regexp "https://github.com/${{ github.repository_owner }}/lifecycle/.github/workflows/build.yml" --certificate-oidc-issuer https://token.actions.githubusercontent.com buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-x86-64 | jq -r .[0].critical.image.\"docker-manifest-digest\")
          echo "LINUX_AMD64_SHA: $LINUX_AMD64_SHA"
          echo "LINUX_AMD64_SHA=$LINUX_AMD64_SHA" >> $GITHUB_ENV

          LINUX_ARM64_SHA=$(cosign verify --certificate-identity-regexp "https://github.com/${{ github.repository_owner }}/lifecycle/.github/workflows/build.yml" --certificate-oidc-issuer https://token.actions.githubusercontent.com buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-arm64 | jq -r .[0].critical.image.\"docker-manifest-digest\")
          echo "LINUX_ARM64_SHA: $LINUX_ARM64_SHA"
          echo "LINUX_ARM64_SHA=$LINUX_ARM64_SHA" >> $GITHUB_ENV

          LINUX_PPC64LE_SHA=$(cosign verify --certificate-identity-regexp "https://github.com/${{ github.repository_owner }}/lifecycle/.github/workflows/build.yml" --certificate-oidc-issuer https://token.actions.githubusercontent.com buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-ppc64le | jq -r .[0].critical.image.\"docker-manifest-digest\")
          echo "LINUX_PPC64LE_SHA: $LINUX_PPC64LE_SHA"
          echo "LINUX_PPC64LE_SHA=$LINUX_PPC64LE_SHA" >> $GITHUB_ENV

          LINUX_S390X_SHA=$(cosign verify --certificate-identity-regexp "https://github.com/${{ github.repository_owner }}/lifecycle/.github/workflows/build.yml" --certificate-oidc-issuer https://token.actions.githubusercontent.com buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-s390x | jq -r .[0].critical.image.\"docker-manifest-digest\")
          echo "LINUX_S390X_SHA: $LINUX_S390X_SHA"
          echo "LINUX_S390X_SHA=$LINUX_S390X_SHA" >> $GITHUB_ENV

      - name: Download SBOM
        run: |
          gh release download --pattern '*-bom.cdx.json' ${{ github.event.release.tag_name }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Retag lifecycle images & create manifest list - semver
        run: |
          DOCKER_CLI_EXPERIMENTAL=enabled

          crane tag buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-x86-64@${{ env.LINUX_AMD64_SHA }} ${{ env.LIFECYCLE_VERSION }}-linux-x86-64
          crane tag buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-arm64@${{ env.LINUX_ARM64_SHA }} ${{ env.LIFECYCLE_VERSION }}-linux-arm64
          crane tag buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-ppc64le@${{ env.LINUX_PPC64LE_SHA }} ${{ env.LIFECYCLE_VERSION }}-linux-ppc64le
          crane tag buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-s390x@${{ env.LINUX_S390X_SHA }} ${{ env.LIFECYCLE_VERSION }}-linux-s390x

          docker manifest create buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }} \
            buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }}-linux-x86-64@${{ env.LINUX_AMD64_SHA }} \
            buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }}-linux-arm64@${{ env.LINUX_ARM64_SHA }} \
            buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }}-linux-ppc64le@${{ env.LINUX_PPC64LE_SHA }} \
            buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }}-linux-s390x@${{ env.LINUX_S390X_SHA }}

          MANIFEST_SHA=$(docker manifest push buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }})
          echo "MANIFEST_SHA: $MANIFEST_SHA"

          cosign sign -r -y \
            -a tag=${{ env.LIFECYCLE_VERSION }} \
            buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }}@${MANIFEST_SHA}
          cosign verify \
            --certificate-identity-regexp "https://github.com/${{ github.repository_owner }}/lifecycle/.github/workflows/post-release.yml" \
            --certificate-oidc-issuer https://token.actions.githubusercontent.com \
            -a tag=${{ env.LIFECYCLE_VERSION }} \
            buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }}

          cosign attach sbom --sbom ./*-bom.cdx.json --type cyclonedx buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }}
          cosign sign -r -y \
            -a tag=${{ env.LIFECYCLE_VERSION }} --attachment sbom \
            buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }}@${MANIFEST_SHA}
          cosign verify \
            --certificate-identity-regexp "https://github.com/${{ github.repository_owner }}/lifecycle/.github/workflows/post-release.yml" \
            --certificate-oidc-issuer https://token.actions.githubusercontent.com \
            -a tag=${{ env.LIFECYCLE_VERSION }} --attachment sbom \
            buildpacksio/lifecycle:${{ env.LIFECYCLE_VERSION }}
      - name: Retag lifecycle images & create manifest list - latest
        if: "!contains(env.LIFECYCLE_VERSION, 'rc') && !contains(env.LIFECYCLE_VERSION, 'pre')"
        run: |
          DOCKER_CLI_EXPERIMENTAL=enabled

          crane tag buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-x86-64@${{ env.LINUX_AMD64_SHA }} latest-linux-x86-64
          crane tag buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-arm64@${{ env.LINUX_ARM64_SHA }} latest-linux-arm64
          crane tag buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-ppc64le@${{ env.LINUX_PPC64LE_SHA }} latest-linux-ppc64le
          crane tag buildpacksio/lifecycle:${{ env.LIFECYCLE_IMAGE_TAG }}-linux-s390x@${{ env.LINUX_S390X_SHA }} latest-linux-s390x

          docker manifest create buildpacksio/lifecycle:latest \
            buildpacksio/lifecycle:latest-linux-x86-64@${{ env.LINUX_AMD64_SHA }} \
            buildpacksio/lifecycle:latest-linux-arm64@${{ env.LINUX_ARM64_SHA }} \
            buildpacksio/lifecycle:latest-linux-ppc64le@${{ env.LINUX_PPC64LE_SHA }} \
            buildpacksio/lifecycle:latest-linux-s390x@${{ env.LINUX_S390X_SHA }}

          MANIFEST_SHA=$(docker manifest push buildpacksio/lifecycle:latest)
          echo "MANIFEST_SHA: $MANIFEST_SHA"

          cosign sign -r -y \
            -a tag=latest \
            buildpacksio/lifecycle:latest@${MANIFEST_SHA}
          cosign verify \
            --certificate-identity-regexp "https://github.com/${{ github.repository_owner }}/lifecycle/.github/workflows/post-release.yml" \
            --certificate-oidc-issuer https://token.actions.githubusercontent.com \
            -a tag=latest \
            buildpacksio/lifecycle:latest

          cosign attach sbom --sbom ./*-bom.cdx.json --type cyclonedx buildpacksio/lifecycle:latest
          cosign sign -r -y \
            -a tag=${{ env.LIFECYCLE_VERSION }} --attachment sbom \
            buildpacksio/lifecycle:latest@${MANIFEST_SHA}
          cosign verify \
            --certificate-identity-regexp "https://github.com/${{ github.repository_owner }}/lifecycle/.github/workflows/post-release.yml" \
            --certificate-oidc-issuer https://token.actions.githubusercontent.com \
            -a tag=${{ env.LIFECYCLE_VERSION }} --attachment sbom \
            buildpacksio/lifecycle:latest
