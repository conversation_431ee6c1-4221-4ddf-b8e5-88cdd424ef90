name: check-latest-release

on:
  schedule:
    - cron: 0 2 * * 1,4
  workflow_dispatch: {}

jobs:
  check-release:
    runs-on:
      - ubuntu-latest
    permissions:
      issues: write
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          check-latest: true
          go-version-file: 'go.mod'
      - name: Get previous release tag
        id: get-previous-release-tag
        uses: actions/github-script@v6
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          result-encoding: string
          script: |
            return github.rest.repos.getLatestRelease({
                owner: "buildpacks",
                repo: "lifecycle",
            }).then(result => {
                return result.data.tag_name
            })
      - name: Read go and release versions
        id: read-versions
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          #!/usr/bin/env bash
          
          set -euo pipefail
          
          LATEST_GO_VERSION=$(go version | cut -d ' ' -f 3)
          
          LATEST_RELEASE_VERSION=${{ steps.get-previous-release-tag.outputs.result }}
          
          wget https://github.com/buildpacks/lifecycle/releases/download/$LATEST_RELEASE_VERSION/lifecycle-$LATEST_RELEASE_VERSION+linux.x86-64.tgz -O lifecycle.tgz
          tar xzf lifecycle.tgz
          LATEST_RELEASE_GO_VERSION=$(go version ./lifecycle/lifecycle | cut -d ' ' -f 2)
          
          echo "latest-go-version=${LATEST_GO_VERSION}" >> "$GITHUB_OUTPUT"
          echo "latest-release-go-version=${LATEST_RELEASE_GO_VERSION}" >> "$GITHUB_OUTPUT"
          
          LATEST_RELEASE_VERSION=$(echo $LATEST_RELEASE_VERSION | cut -d \v -f 2)
          echo "latest-release-version=${LATEST_RELEASE_VERSION}" >> "$GITHUB_OUTPUT"
      - name: Create issue if needed
        if: ${{ steps.read-versions.outputs.latest-go-version != steps.read-versions.outputs.latest-release-go-version }}
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          #!/usr/bin/env bash
          
          set -euo pipefail
          
          title="Upgrade lifecycle to ${{ steps.read-versions.outputs.latest-go-version }}"
          label=${{ steps.read-versions.outputs.latest-go-version }}
          
          # Create label to use for exact search
          gh label create "$label" || true
          
          search_output=$(gh issue list --search "$title" --label "$label")
          
          body="Latest lifecycle release v${{ steps.read-versions.outputs.latest-release-version }} is built with Go version ${{ steps.read-versions.outputs.latest-release-go-version }}; newer version ${{ steps.read-versions.outputs.latest-go-version }} is available."
          
          if [ -z "${search_output// }" ]
          then
            echo "No issues matched search; creating new issue..."
            gh issue create \
              --label "type/bug" \
              --label "status/triage" \
              --label "$label" \
              --title "$title" \
              --body "$body"
          else
            echo "Found matching issues:"
            echo $search_output
          fi
      - name: Scan latest release image
        id: scan-image
        uses: anchore/scan-action@v6
        with:
          image: buildpacksio/lifecycle:${{ steps.read-versions.outputs.latest-release-version }}
          fail-build: true
          severity-cutoff: medium
          output-format: json
      - name: Create issue if needed
        if: failure() && steps.scan-image.outcome == 'failure'
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
            #!/usr/bin/env bash

            set -euo pipefail

            title="CVE(s) found in v${{ steps.read-versions.outputs.latest-release-version }}"
            label=cve

            # Create label to use for exact search
            gh label create "$label" || true

            search_output=$(gh issue list --search "$title" --label "$label")

            GITHUB_WORKFLOW_URL=https://github.com/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID
            body="Latest lifecycle release v${{ steps.read-versions.outputs.latest-release-version }} triggered CVE(s) from Grype. For further details, see: $GITHUB_WORKFLOW_URL json: $(cat ${{ steps.scan-image.outputs.json }} | jq '.matches[] | .vulnerability | {id, severity, description}' )"

            if [ -z "${search_output// }" ]
            then
              echo "No issues matched search; creating new issue..."
              gh issue create \
              --label "type/bug" \
              --label "status/triage" \
              --label "$label" \
              --title "$title" \
              --body "$body"
            else
              echo "Found matching issues:"
              echo $search_output
            fi
