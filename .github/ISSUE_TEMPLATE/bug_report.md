---
name: Bug
about: Bug report
title: ''
labels: type/bug, status/triage
assignees: ''

---

### Summary
<!-- Please provide a general summary of the issue. -->



---

### Reproduction

##### Steps
<!-- What steps should be taken to reproduce the issue? -->

1.
2.
3.

##### Current behavior
<!-- What happened? Logs, etc. could go here. -->



##### Expected behavior
<!-- What did you expect to happen? -->



---

### Context

##### lifecycle version
<!-- If you can find this, it helps us pin down the issue. For example, run `pack builder inspect <builder name>` which should report the lifecycle version in question. -->



##### platform version(s)
<!-- For example run `pack report` and `docker info` and copy output here, redacting any sensitive information. -->



##### anything else?
<!-- Add any other context that may help (e.g., Tekton task version, kpack version, etc.). -->

